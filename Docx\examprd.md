# **产品需求文档：疾控医护任职资格考试系统（Web端）**

**版本：v1.4 | 日期：2025年7月6日**

## 1. 产品概述

一款专为疾控机构医护人员设计的任职资格考试及学习辅助平台。系统通过 **Web端**，提供从学习备考、模拟练习、考试报名、在线考试到证书管理的全流程服务，旨在提升医护人员专业素养，并助力疾控机构实现高效、规范的人员资质管理。

## 2. 核心原则

- **用户体验为王**: 界面流畅、反馈及时、引导清晰。
- **简单优于复杂**: 偏好优雅、直观的解决方案。
- **自动化繁为简**: 利用工具自动化处理加载、缓存、错误等状态。
- **可访问性优先 (A11y)**: 确保产品对所有用户都可用，遵循WCAG 2.1 AA标准。

## 3. 功能需求

### 3.1 功能架构图 (Web端)

```mermaid
graph TD
    A[用户] --> B{Web Application (Vue 3)}

    subgraph B [疾控医护任职资格考试系统 (Web端)]
        C[登录模块]
        D[主应用布局 (Layout)]
        E[信息中心]
        F[学习中心]
        G[考试中心]
        H[个人中心]
        I[全局公共模块]

        C --> D
        D -- 侧边栏导航 --> E
        D -- 侧边栏导航 --> F
        D -- 侧边栏导航 --> G
        D -- 侧边栏导航 --> H

        subgraph C [登录模块]
            C1[微信扫码登录]
            C2[用户协议确认]
            C3[登录上下文恢复]
        end

        subgraph D [主应用布局]
            D1[侧边栏导航 (含退出登录)]
            D2[顶部栏 (用户信息)]
            D3[内容区 (RouterView)]
            D4[全局页脚 (Footer)]
        end

        subgraph I [全局公共模块]
            I1[关于我们 (页面)]
            I2[投诉与建议 (页面/弹窗)]
            I3[服务条款/隐私政策 (页面)]
            I4[全局通知系统]
        end

	    subgraph F [学习中心]
  	        F1[教材模块 (预留)]
  	        F2[题库模块]
        end

        subgraph G [考试中心]
            G1[待考列表]
            G2[历史考试记录]
            G3[线上考试流程]
            G4[线下考试报名流程]
        end

        subgraph H [个人中心 (Tabs布局/深度链接)]
            H1[个人信息]
            H2[证书管理]
        end
    end

    B --> J{后端服务 (API)}
```

### 3.2 核心功能详述

---

#### **模块一：登录与权限**

**核心用户故事**:

> 作为一个访客，我希望能先浏览一下平台的内容，以便于判断它是否对我有价值；当我决定使用时，我希望能通过微信扫码快速安全地登录，并且系统能记得我登录前正在看什么，以便于我能无缝地继续我的操作。

**功能规格**:

1.  **微信扫码登录**:

    - 1.1 系统唯一登录方式为微信扫码登录。
    - 1.2 登录页必须展示一个有时效性（建议2分钟）的二维码，过期后提示用户刷新。
    - 1.3 用户必须勾选《用户服务协议》和《隐私政策》后，才能进行有效的扫码操作。
    - 1.4 登录成功后，前端获取并安全存储Token，用于后续API请求。

2.  **用户访问权限控制**:
    - 2.1 **访客模式**: 未登录用户可自由浏览**信息中心**和**学习中心**的全部内容。
    - 2.2 **受限引导**: 当访客尝试访问受限页面（如考试中心、个人中心）时，当前页面必须显示一个非侵入式的提示（如页面内横幅或遮罩），包含登录引导文案和明确的登录入口链接。
    - 2.3 **登录上下文恢复**: 当访客从受限页面跳转至登录页并成功登录后，系统必须自动将其重定向回登录前尝试访问的页面，以保证体验的连续性。
    - 2.4 **未认证用户**: 已登录但未通过机构认证的用户，可正常访问信息中心和学习中心。访问考试中心时，系统需提示其当前的认证状态（如“待审核”、“审核未通过”），并引导至个人中心完善资料。

---

#### **模块二：主应用布局与通用体验**

**核心用户故事**:

> 作为一个用户，我希望系统界面布局清晰一致，能方便地在各个功能区切换，并且在我进行任何操作时，系统都能给我及时、明确的反馈，以便于我能高效、安心地使用平台。

**功能规格**:

1.  **主应用布局**:
    - 1.1 核心模式: 极简单行顶部导航 + 页面内导航。
    - 1.2 **顶部导航栏 (Global Header):**:
      - a. 结构: 这是应用唯一的全局导航栏，横跨页面顶部。
      - b. 左侧区域: [Logo]，其后紧跟4个文本链接：信息中心、学习中心、考试中心、个人中心。
      - c. 右侧区域: 仅包含一个用户菜单，由用户的 [头像] 和 姓名 组成。。
      - d. 禁止项: 除以上元素外，顶部导航栏禁止包含任何其他图标、链接或功能。
    - 1.3 **子导航 (Sub-Navigation):**
      - a. 所有二级及以下的导航，都必须通过**“页面内导航”的形式呈现在主内容区**。
      - b. 例如，进入“学习中心”后，可在其页面内部使用标签页(Tabs)或次级列表来进行更深层次的导航。
    - 1.4 **全局页脚 (Footer)**:
      - a. 定位: 固定在应用所有页面的最底部，作为信任和支持的基石。
      - b. 风格: 设计上保持不打扰，使用小号、浅灰色文字 (#6B7280)，与主内容区有明确的视觉分离。
      - c. 核心链接: 必须包含以下链接，以单行水平排列：
        - “关于我们”、“投诉与建议”、“服务条款”、“隐私政策”、“版权信息”、“联系我们”
      - d. 版权信息: 在链接下方或同行，包含版权和备案信息（如果适用）。例如 © 2025 [平台名称] All Rights Reserved.
2.  **全局通知系统**:
    - 2.1 系统必须提供一个全局通知（Toast）系统，用于显示操作的即时反馈。
    - 2.2 通知类型需支持：成功、失败、警告、信息。
    - 2.3 通知应从屏幕右上角或顶部中心滑出，停留数秒后自动消失。

---

#### **模块三：信息中心**

**核心用户故事**:

> 作为一个医护人员，我希望能在一个集中的地方快速查看到所有与我相关的考试公告、最新的政策法规和机构的重要通知，以便于我能及时掌握信息，避免错过重要安排。

**功能规格**:

1.  **内容展示**:

    - 1.1 页面以列表形式聚合展示“公告”、“政策法规”、“重要通知”等信息。
    - 1.2 列表项需显示标题、发布日期和摘要（可选）。
    - 1.3 支持按发布日期倒序排列，重要信息可由后台配置置顶。
    - 1.4 列表必须支持分页加载。

2.  **详情查看**:
    - 2.1 点击任意列表项可进入详情页，展示完整内容。
    - 2.2 详情页需支持富文本格式。

---

#### **模块四：学习中心**

**核心用户故事**:

> 作为一个备考者，我希望能随时随地进行题库练习，并能立即看到答案和解析，以便于我能利用碎片化时间高效复习，巩固知识点。

**功能规格**:

1.  **题库模块**:

    - 1.1 **分类选择**: 必须以卡片或列表形式清晰展示所有题库分类，并可显示各分类下的题目总数。
    - 1.2 **刷题界面**:
      - a. 支持多种题型：单选、多选、判断、问答。
      - b. 用户作答后，系统必须立即给出对错反馈（问答题除外），并提供可展开的“答案解析”区域。
      - c. 完成一组练习后，需显示本次练习的总结报告（正确率、答对/错题数），并提供“再来一组”和“返回分类”的操作入口。
    - 1.3 **次数限制**: 免费用户每日练习次数受限，达到上限后需给出明确提示。

2.  **教材学习 (预留)**:
    - 2.1 学习中心需预留“教材学习”模块的入口。
    - 2.2 当前版本点击该入口，提示“功能建设中，敬请期待”。

---

#### **模块五：考试中心**

**核心用户故事**:

> 作为一个考生，我希望能清晰地看到我需要参加的所有考试，无论是线上还是线下，都能顺畅地完成报名和考试流程，并能方便地回顾我的考试历史，以便于我能有效地管理我的认证过程。

**功能规格**:

1.  **考试中心主页**:

    - 1.1 页面分为“本期考试（待考）”和“历史考试记录”两大区域。
    - 1.2 “本期考试”以卡片形式展示，清晰标明考试名称、类型、状态和有效期，并根据状态显示不同的操作按钮。

2.  **线上考试流程**:

    - 2.1 **考前阅读**: 进入考试前，必须展示考前须知，用户需确认后方可继续。
    - 2.2 **在线答题环境**:
      - a. **全屏模式**: 进入考试时，系统必须通过API请求浏览器进入全屏模式。
      - b. **水印**: 考试界面背景必须叠加不可选中的、含考生关键信息的半透明水印。
      - c. **剪贴板与右键控制**: 必须禁止在答题区域进行复制、粘贴操作，并禁用浏览器默认的右键菜单。
    - 2.3 **防作弊检测与响应**:
      - a. **检测机制**: 系统必须同时监听浏览器切屏和退出全屏事件。
      - b. **人性化分级警告**:
        - **首次触发**: 非模态、自动消失的温柔提示。
        - **多次触发**: 模态对话框，需用户确认。
        - **超过阈值**: 标记为“作弊嫌疑”，记录日志供人工复核。

3.  **线下考试报名流程**:

    - 3.1 **详情与场次列表**: 详情页需展示考试说明，并以列表形式展示所有可选的考场及对应的考试时间场次。
    - 3.2 **场次信息**: 每个场次必须清晰显示日期、时间段、剩余名额/总名额。
    - 3.3 **报名/取消操作**: 根据场次名额和用户状态显示“报名”或“已报满”按钮。报名成功后提供“取消报名”功能（可由后台配置时限）。所有关键操作需二次确认。

4.  **历史考试记录**:
    - 4.1 以列表形式展示用户所有已完成的考试，包含考试名称、完成日期、最终得分/成绩、最终状态。
    - 4.2 必须支持分页加载。

---

#### **模块六：个人中心**

**核心用户故事**:

> 作为一个平台用户，我希望有一个专属的、清晰的个人空间，能方便地管理我的个人资料和查看我的所有证书，以便于我能高效地处理与我个人账户相关的事务。

**功能规格**:

1.  **布局与导航**:

    - 1.1 必须采用**标签页(Tabs)导航布局**，使其在视觉上成为一个独立的、内聚的功能区。
    - 1.2 每个标签页必须对应一个独立的URL（如`/profile/info`, `/profile/certificates`），**支持深度链接**。
    - 1.3 标签页聚焦于个人事务，仅包含：**个人信息**、**证书管理**。

2.  **个人信息**:

    - 2.1 未认证用户可在此页面提交或修改用于机构审核的个人资料。
    - 2.2 已认证用户的个人信息为只读状态，敏感信息必须进行脱敏处理。

3.  **证书管理**:

    - 3.1 分区展示“当前有效证书”和“历史证书列表”。
    - 3.2 如果新证书正在审批中且旧证书依然有效，需同时展示两者状态。
    - 3.3 每张证书都必须提供“查看大图”和“**下载证书**”的功能。

4.  **有意义的空状态 (Empty State)**:
    - 4.1 在“证书管理”等列表数据为空时，必须展示精心设计的空状态界面，包含友好的提示文案、相关图标，以及清晰的下一步操作引导链接。

---

## 7. 非功能需求

### 7.1 性能要求

- **页面加载**: 遵循Google Core Web Vitals标准 (LCP < 2.5s)。
- **代码拆分**: 必须基于路由进行懒加载。
- **API响应**: 95%的请求在500ms内响应。所有数据请求必须有明确的加载状态。

### 7.2 安全要求

- **传输**: 全站强制HTTPS。
- **认证**: 使用JWT，Token存储在HttpOnly、Secure的Cookie中。
- **前端**: 防范XSS、CSRF。所有用户输入都需经过严格校验。

### 7.3 兼容性要求

- **浏览器**: 兼容Chrome, Firefox, Safari, Edge的最新两个主版本。
- **响应式**: 界面在主流桌面和横屏平板上必须表现良好。

### 7.4 用户体验 (UX) 与可访问性 (A11y)

- **交互流畅**: 所有异步操作必须有即时反馈。
- **可访问性 (核心要求)**:
  - **键盘可操作**: 所有功能均可通过键盘完成。
  - **语义化HTML**: 严格使用正确的HTML5标签。
  - **ARIA支持**: 为所有非文本交互元素提供清晰的`aria-label`。
  - **颜色对比度**: 满足WCAG 2.1 AA级标准。
- **系统化错误处理**:
  | 错误类型 | 用户端表现 |
  | :--- | :--- |
  | 401/403 (认证/授权失败) | 自动清除本地登录状态，并强制跳转至登录页。 |
  | 422 (表单验证失败) | 在对应表单字段下方显示具体错误信息。 |
  | 404 (资源未找到) | 显示专门设计的“404页面”。 |
  | 5xx (服务器错误) | 弹出全局Toast提示“服务器开小差了”，同时在后台记录错误日志。 |
  | 网络连接中断 | `useQuery`自动处理重试，并显示“网络连接中断，正在重连...”的非模态提示。 |
