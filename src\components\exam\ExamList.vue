<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Exam, ExamRecord } from '@/types'
import ExamCard from './ExamCard.vue'

interface Props {
  exams: Exam[]
  records?: ExamRecord[]
  loading?: boolean
  title?: string
  emptyText?: string
  emptyIcon?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '考试列表',
  emptyText: '暂无考试',
  emptyIcon: '📝'
})

const emit = defineEmits<{
  register: [exam: Exam]
  start: [exam: Exam]
  view: [exam: Exam]
  viewResult: [record: ExamRecord]
  refresh: []
}>()

// 状态管理
const searchKeyword = ref('')
const selectedType = ref<string>('')
const selectedStatus = ref<string>('')

// 筛选后的考试列表
const filteredExams = computed(() => {
  let filtered = props.exams

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(exam => 
      exam.title.toLowerCase().includes(keyword) ||
      exam.description.toLowerCase().includes(keyword)
    )
  }

  // 按类型筛选
  if (selectedType.value) {
    filtered = filtered.filter(exam => exam.type === selectedType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    filtered = filtered.filter(exam => {
      const record = getExamRecord(exam.id)
      if (selectedStatus.value === 'available') {
        return !record && isRegistrationOpen(exam)
      } else if (selectedStatus.value === 'registered') {
        return record?.status === 'registered'
      } else if (selectedStatus.value === 'completed') {
        return record?.status === 'completed'
      }
      return true
    })
  }

  return filtered
})

// 获取考试记录
function getExamRecord(examId: string): ExamRecord | undefined {
  return props.records?.find(record => record.examId === examId)
}

// 检查是否可以报名
function isRegistrationOpen(exam: Exam): boolean {
  const now = new Date()
  const registrationDeadline = new Date(exam.registrationDeadline)
  return now < registrationDeadline && exam.status === 'ongoing'
}

// 清除筛选条件
function clearFilters() {
  searchKeyword.value = ''
  selectedType.value = ''
  selectedStatus.value = ''
}

// 处理事件
function handleRegister(exam: Exam) {
  emit('register', exam)
}

function handleStart(exam: Exam) {
  emit('start', exam)
}

function handleView(exam: Exam) {
  emit('view', exam)
}

function handleViewResult(record: ExamRecord) {
  emit('viewResult', record)
}

function handleRefresh() {
  emit('refresh')
}

// 统计信息
const stats = computed(() => {
  const total = props.exams.length
  const available = props.exams.filter(exam => !getExamRecord(exam.id) && isRegistrationOpen(exam)).length
  const registered = props.records?.filter(record => record.status === 'registered').length || 0
  const completed = props.records?.filter(record => record.status === 'completed').length || 0
  
  return { total, available, registered, completed }
})
</script>

<template>
  <div class="space-y-6">
    <!-- 搜索和筛选栏 -->
    <div class="bg-white rounded-lg shadow-sm p-4">
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- 搜索框 -->
        <div class="flex-1">
          <div class="relative">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索考试名称或描述..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 类型筛选 -->
        <div class="lg:w-40">
          <select
            v-model="selectedType"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">全部类型</option>
            <option value="online">线上考试</option>
            <option value="offline">线下考试</option>
          </select>
        </div>

        <!-- 状态筛选 -->
        <div class="lg:w-40">
          <select
            v-model="selectedStatus"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">全部状态</option>
            <option value="available">可报名</option>
            <option value="registered">已报名</option>
            <option value="completed">已完成</option>
          </select>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-2">
          <button
            v-if="searchKeyword || selectedType || selectedStatus"
            @click="clearFilters"
            class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            清除筛选
          </button>
          
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            :disabled="loading"
          >
            <svg class="w-4 h-4 inline-block mr-2" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">考试总数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">可报名</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.available }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已报名</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.registered }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 rounded-lg">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已完成</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.completed }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 考试列表 -->
    <div v-if="loading" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 骨架屏 -->
      <div v-for="i in 4" :key="i" class="bg-white rounded-lg shadow-sm p-6 animate-pulse">
        <div class="flex items-start space-x-3 mb-4">
          <div class="w-8 h-8 bg-gray-200 rounded"></div>
          <div class="flex-1">
            <div class="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div class="w-16 h-6 bg-gray-200 rounded-full"></div>
        </div>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
          <div class="h-4 bg-gray-200 rounded"></div>
        </div>
        <div class="h-10 bg-gray-200 rounded"></div>
      </div>
    </div>

    <div v-else-if="filteredExams.length === 0" class="text-center py-12">
      <div class="text-6xl mb-4">{{ emptyIcon }}</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">{{ emptyText }}</h3>
      <p class="text-gray-600">
        {{ searchKeyword || selectedType || selectedStatus ? '没有找到符合条件的考试' : '暂时没有可用的考试' }}
      </p>
    </div>

    <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <ExamCard
        v-for="exam in filteredExams"
        :key="exam.id"
        :exam="exam"
        :record="getExamRecord(exam.id)"
        @register="handleRegister"
        @start="handleStart"
        @view="handleView"
        @view-result="handleViewResult"
      />
    </div>
  </div>
</template>
